# 主播工会管理平台

一个基于 Vue 3 + TypeScript + Vite 构建的主播工会管理系统前端项目。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - JavaScript 的超集，提供类型安全
- **Vite** - 下一代前端构建工具
- **Vue Router 4** - Vue.js 官方路由管理器
- **Element Plus** - Vue 3 UI组件库
- **Vuex** - 状态管理
- **Axios** - HTTP客户端
- **ECharts** - 数据可视化图表库
- **Mitt** - 事件总线

## 功能特性

- 🎯 现代化的管理界面
- 📱 响应式设计（PC端优化）
- 🔧 TypeScript 类型安全
- ⚡ Vite 快速开发体验
- 🎨 Element Plus UI组件
- 📊 ECharts 数据可视化
- 🗂️ Vuex 状态管理
- 💾 数据持久化支持

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

项目将在 http://localhost:8000 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 项目结构

```
src/
├── router/          # 路由配置
├── views/           # 页面组件
├── App.vue          # 根组件
├── main.ts          # 入口文件
└── style.css        # 全局样式
```

## 依赖版本

- Vue 3.5.18
- Vue Router 4.5.1
- Element Plus 2.10.5
- Vuex 4.1.0
- Axios 1.11.0
- ECharts 6.0.0
- Mitt 3.0.1
- Vuex-along 1.2.13

## 开发说明

- 默认端口：8000
- 支持热重载
- TypeScript 类型检查
- 现代浏览器支持
- 所有依赖版本兼容Vue 3
