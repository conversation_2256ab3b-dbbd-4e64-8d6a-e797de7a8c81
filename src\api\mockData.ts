// 模拟API响应数据
export interface MockUser {
  id: string
  email: string
  name: string
  role: 'admin' | 'operator' | 'streamer'
  guild_id?: string
}

// 演示账户数据
export const mockUsers: MockUser[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: '管理员',
    role: 'admin',
    guild_id: 'guild-1'
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: '运营人员',
    role: 'operator',
    guild_id: 'guild-1'
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: '主播小王',
    role: 'streamer',
    guild_id: 'guild-1'
  }
]

// 演示账户密码（实际项目中不应该这样存储）
export const mockPasswords: Record<string, string> = {
  '<EMAIL>': 'admin123',
  '<EMAIL>': 'operator123',
  '<EMAIL>': 'streamer123'
}

// 模拟登录响应
export const mockLoginResponse = (email: string, password: string) => {
  const user = mockUsers.find(u => u.email === email)
  const correctPassword = mockPasswords[email]
  
  if (!user || password !== correctPassword) {
    throw new Error('邮箱或密码错误')
  }
  
  return {
    access_token: `mock_token_${user.id}_${Date.now()}`,
    user
  }
}