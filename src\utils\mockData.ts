// 模拟API响应数据
export const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    name: '系统管理员',
    role: 'admin' as const,
    guild_id: undefined
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'operator123',
    name: '运营专员',
    role: 'operator' as const,
    guild_id: 'guild_1'
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: 'streamer123',
    name: '主播小美',
    role: 'streamer' as const,
    guild_id: 'guild_1'
  }
]

// 模拟登录响应
export const mockLoginResponse = (email: string, password: string) => {
  const user = mockUsers.find(u => u.email === email && u.password === password)
  
  if (!user) {
    throw new Error('邮箱或密码错误')
  }
  
  const { password: _, ...userWithoutPassword } = user
  
  return {
    access_token: `mock_token_${user.id}_${Date.now()}`,
    user: userWithoutPassword
  }
}

// 演示账户信息
export const demoAccounts = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    role: '管理员',
    description: '拥有所有权限，可以管理工会、主播和系统设置'
  },
  {
    email: '<EMAIL>',
    password: 'operator123',
    role: '运营专员',
    description: '可以管理主播信息和查看数据报表'
  },
  {
    email: '<EMAIL>',
    password: 'streamer123',
    role: '主播',
    description: '可以查看个人数据和收益信息'
  }
]