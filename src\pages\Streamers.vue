<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">主播管理</h1>
        <p class="text-gray-600 mt-1">管理平台所有主播信息</p>
      </div>
      <el-button type="primary" @click="showAddDialog = true">
        <UserPlus class="w-4 h-4 mr-2" />
        添加主播
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <el-input
          v-model="searchQuery"
          placeholder="搜索主播姓名或ID"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <Search class="w-4 h-4 text-gray-400" />
          </template>
        </el-input>
        <el-select v-model="statusFilter" placeholder="状态筛选" clearable>
          <el-option label="全部" value="" />
          <el-option label="在线" value="online" />
          <el-option label="离线" value="offline" />
          <el-option label="禁播" value="banned" />
        </el-select>
        <el-select v-model="guildFilter" placeholder="工会筛选" clearable>
          <el-option label="全部" value="" />
          <el-option label="星光工会" value="1" />
          <el-option label="梦想工会" value="2" />
          <el-option label="彩虹工会" value="3" />
        </el-select>
        <el-button @click="resetFilters">重置筛选</el-button>
      </div>
    </div>

    <!-- 主播列表 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <el-table
        :data="filteredStreamers"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="主播信息" min-width="200">
          <template #default="{ row }">
            <div class="flex items-center space-x-3">
              <div class="relative">
                <img
                  :src="row.avatar"
                  :alt="row.name"
                  class="w-10 h-10 rounded-full object-cover"
                />
                <div
                  :class="[
                    'absolute -bottom-1 -right-1 w-3 h-3 border-2 border-white rounded-full',
                    row.status === 'online' ? 'bg-green-500' : 'bg-gray-400'
                  ]"
                ></div>
              </div>
              <div>
                <p class="font-medium text-gray-900">{{ row.name }}</p>
                <p class="text-sm text-gray-500">ID: {{ row.id }}</p>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="guild" label="所属工会" width="120" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="等级" width="80" />
        <el-table-column label="粉丝数" width="100">
          <template #default="{ row }">
            {{ formatNumber(row.followers) }}
          </template>
        </el-table-column>
        <el-table-column label="今日收益" width="120">
          <template #default="{ row }">
            <span class="font-medium text-green-600">¥{{ row.todayIncome }}</span>
          </template>
        </el-table-column>
        <el-table-column label="总收益" width="120">
          <template #default="{ row }">
            <span class="font-medium">¥{{ formatNumber(row.totalIncome) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="注册时间" width="120">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="flex space-x-2">
              <el-button size="small" @click="viewStreamer(row)">
                <Eye class="w-4 h-4" />
              </el-button>
              <el-button size="small" type="primary" @click="editStreamer(row)">
                <Edit class="w-4 h-4" />
              </el-button>
              <el-button
                size="small"
                :type="row.status === 'banned' ? 'success' : 'warning'"
                @click="toggleBan(row)"
              >
                <component :is="row.status === 'banned' ? UserCheck : UserX" class="w-4 h-4" />
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="p-4 border-t border-gray-200">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalStreamers"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加/编辑主播对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingStreamer ? '编辑主播' : '添加主播'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="streamerForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="头像" prop="avatar">
          <div class="flex items-center space-x-4">
            <img
              :src="streamerForm.avatar || defaultAvatar"
              alt="头像预览"
              class="w-16 h-16 rounded-full object-cover border border-gray-200"
            />
            <el-input
              v-model="streamerForm.avatar"
              placeholder="请输入头像URL"
              style="width: 300px"
            />
          </div>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="streamerForm.name" placeholder="请输入主播姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="streamerForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="streamerForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="所属工会" prop="guildId">
          <el-select v-model="streamerForm.guildId" placeholder="请选择工会" style="width: 100%">
            <el-option label="星光工会" value="1" />
            <el-option label="梦想工会" value="2" />
            <el-option label="彩虹工会" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="等级" prop="level">
          <el-input-number
            v-model="streamerForm.level"
            :min="1"
            :max="100"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="分成比例" prop="commissionRate">
          <el-input-number
            v-model="streamerForm.commissionRate"
            :min="0"
            :max="100"
            :precision="1"
            style="width: 100%"
          />
          <span class="text-sm text-gray-500 ml-2">%</span>
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input
            v-model="streamerForm.note"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="saveStreamer" :loading="saving">
            {{ editingStreamer ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 主播详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="主播详情" width="800px">
      <div v-if="selectedStreamer" class="space-y-6">
        <!-- 基本信息 -->
        <div class="flex items-start space-x-6">
          <img
            :src="selectedStreamer.avatar"
            :alt="selectedStreamer.name"
            class="w-24 h-24 rounded-full object-cover"
          />
          <div class="flex-1">
            <h3 class="text-xl font-bold text-gray-900">{{ selectedStreamer.name }}</h3>
            <div class="grid grid-cols-2 gap-4 mt-4">
              <div>
                <p class="text-sm text-gray-500">ID</p>
                <p class="font-medium">{{ selectedStreamer.id }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">等级</p>
                <p class="font-medium">{{ selectedStreamer.level }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">所属工会</p>
                <p class="font-medium">{{ selectedStreamer.guild }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">状态</p>
                <el-tag :type="getStatusType(selectedStreamer.status)" size="small">
                  {{ getStatusText(selectedStreamer.status) }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 统计数据 -->
        <div class="grid grid-cols-3 gap-4">
          <div class="bg-blue-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-blue-600">{{ formatNumber(selectedStreamer.followers) }}</p>
            <p class="text-sm text-gray-600">粉丝数</p>
          </div>
          <div class="bg-green-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-green-600">¥{{ selectedStreamer.todayIncome }}</p>
            <p class="text-sm text-gray-600">今日收益</p>
          </div>
          <div class="bg-purple-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-purple-600">¥{{ formatNumber(selectedStreamer.totalIncome) }}</p>
            <p class="text-sm text-gray-600">总收益</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  UserPlus,
  Search,
  Eye,
  Edit,
  UserX,
  UserCheck
} from 'lucide-vue-next'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const guildFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalStreamers = ref(0)
const selectedStreamers = ref([])
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const editingStreamer = ref(null)
const selectedStreamer = ref(null)
const formRef = ref()

// 默认头像
const defaultAvatar = 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=default%20user%20avatar%20simple%20style&image_size=square'

// 主播表单
const streamerForm = ref({
  name: '',
  phone: '',
  email: '',
  avatar: '',
  guildId: '',
  level: 1,
  commissionRate: 50,
  note: ''
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入主播姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  guildId: [{ required: true, message: '请选择工会', trigger: 'change' }]
}

// 模拟主播数据
const streamers = ref([
  {
    id: 'S001',
    name: '小美',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=beautiful%20female%20streamer%20avatar%20cartoon%20style&image_size=square',
    guild: '星光工会',
    guildId: '1',
    status: 'online',
    level: 15,
    followers: 12580,
    todayIncome: 2580,
    totalIncome: 156800,
    phone: '13812345678',
    email: '<EMAIL>',
    commissionRate: 60,
    createdAt: '2024-01-15',
    note: '优秀主播，表现突出'
  },
  {
    id: 'S002',
    name: '阳光少女',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=cute%20girl%20streamer%20avatar%20anime%20style&image_size=square',
    guild: '梦想工会',
    guildId: '2',
    status: 'offline',
    level: 12,
    followers: 8960,
    todayIncome: 1890,
    totalIncome: 89600,
    phone: '13987654321',
    email: '<EMAIL>',
    commissionRate: 55,
    createdAt: '2024-02-20',
    note: '活跃主播'
  },
  {
    id: 'S003',
    name: '游戏达人',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=gaming%20streamer%20avatar%20modern%20style&image_size=square',
    guild: '彩虹工会',
    guildId: '3',
    status: 'online',
    level: 20,
    followers: 23410,
    todayIncome: 3420,
    totalIncome: 234100,
    phone: '13611111111',
    email: '<EMAIL>',
    commissionRate: 65,
    createdAt: '2023-12-10',
    note: '顶级主播'
  }
])

// 过滤后的主播列表
const filteredStreamers = computed(() => {
  let result = streamers.value

  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(streamer => 
      streamer.name.includes(searchQuery.value) || 
      streamer.id.includes(searchQuery.value)
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(streamer => streamer.status === statusFilter.value)
  }

  // 工会过滤
  if (guildFilter.value) {
    result = result.filter(streamer => streamer.guildId === guildFilter.value)
  }

  totalStreamers.value = result.length
  return result
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'online': return 'success'
    case 'offline': return 'info'
    case 'banned': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'online': return '在线'
    case 'offline': return '离线'
    case 'banned': return '禁播'
    default: return '未知'
  }
}

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

// 格式化日期
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
}

// 重置筛选
const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  guildFilter.value = ''
  currentPage.value = 1
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedStreamers.value = selection
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 查看主播详情
const viewStreamer = (streamer: any) => {
  selectedStreamer.value = streamer
  showDetailDialog.value = true
}

// 编辑主播
const editStreamer = (streamer: any) => {
  editingStreamer.value = streamer
  streamerForm.value = { ...streamer }
  showAddDialog.value = true
}

// 切换禁播状态
const toggleBan = async (streamer: any) => {
  const action = streamer.status === 'banned' ? '解除禁播' : '禁播'
  try {
    await ElMessageBox.confirm(
      `确定要${action}主播 ${streamer.name} 吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟API调用
    streamer.status = streamer.status === 'banned' ? 'offline' : 'banned'
    ElMessage.success(`${action}成功`)
  } catch {
    // 用户取消操作
  }
}

// 保存主播
const saveStreamer = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingStreamer.value) {
      // 更新现有主播
      const index = streamers.value.findIndex(s => s.id === editingStreamer.value.id)
      if (index !== -1) {
        streamers.value[index] = { ...streamers.value[index], ...streamerForm.value }
      }
      ElMessage.success('主播信息更新成功')
    } else {
      // 添加新主播
      const newStreamer = {
        ...streamerForm.value,
        id: `S${String(streamers.value.length + 1).padStart(3, '0')}`,
        status: 'offline',
        followers: 0,
        todayIncome: 0,
        totalIncome: 0,
        createdAt: new Date().toISOString().split('T')[0],
        guild: getGuildName(streamerForm.value.guildId)
      }
      streamers.value.push(newStreamer)
      ElMessage.success('主播添加成功')
    }
    
    showAddDialog.value = false
    resetForm()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 获取工会名称
const getGuildName = (guildId: string) => {
  const guildMap: Record<string, string> = {
    '1': '星光工会',
    '2': '梦想工会',
    '3': '彩虹工会'
  }
  return guildMap[guildId] || '未知工会'
}

// 重置表单
const resetForm = () => {
  editingStreamer.value = null
  streamerForm.value = {
    name: '',
    phone: '',
    email: '',
    avatar: '',
    guildId: '',
    level: 1,
    commissionRate: 50,
    note: ''
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 组件挂载
onMounted(() => {
  totalStreamers.value = streamers.value.length
})
</script>