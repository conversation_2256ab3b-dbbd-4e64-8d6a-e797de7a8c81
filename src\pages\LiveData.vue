<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">直播数据</h1>
      <p class="text-gray-600 mt-1">实时监控直播数据和历史统计分析</p>
    </div>

    <!-- 实时数据概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div
        v-for="stat in realTimeStats"
        :key="stat.title"
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">{{ stat.title }}</p>
            <p class="text-3xl font-bold text-gray-900 mt-2">{{ stat.value }}</p>
            <div class="flex items-center mt-2">
              <component
                :is="stat.trend === 'up' ? TrendingUp : TrendingDown"
                :class="[
                  'w-4 h-4 mr-1',
                  stat.trend === 'up' ? 'text-green-500' : 'text-red-500'
                ]"
              />
              <span
                :class="[
                  'text-sm font-medium',
                  stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                ]"
              >
                {{ stat.change }}
              </span>
              <span class="text-sm text-gray-500 ml-1">vs 昨日</span>
            </div>
          </div>
          <div
            :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center',
              stat.bgColor
            ]"
          >
            <component :is="stat.icon" :class="['w-6 h-6', stat.iconColor]" />
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和控制 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">数据筛选</h3>
        <div class="flex items-center space-x-2">
          <el-button
            :type="autoRefresh ? 'primary' : 'default'"
            size="small"
            @click="toggleAutoRefresh"
          >
            <RotateCcw class="w-4 h-4 mr-1" />
            {{ autoRefresh ? '停止刷新' : '自动刷新' }}
          </el-button>
          <el-button size="small" @click="refreshData">
            <RefreshCw class="w-4 h-4 mr-1" />
            手动刷新
          </el-button>
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
        <el-select v-model="selectedGuild" placeholder="选择工会" clearable>
          <el-option label="全部工会" value="" />
          <el-option label="星光工会" value="1" />
          <el-option label="梦想工会" value="2" />
          <el-option label="彩虹工会" value="3" />
        </el-select>
        <el-select v-model="dataType" placeholder="数据类型">
          <el-option label="观众数据" value="viewers" />
          <el-option label="收益数据" value="income" />
          <el-option label="互动数据" value="interaction" />
        </el-select>
        <el-button type="primary" @click="applyFilters">
          <Filter class="w-4 h-4 mr-1" />
          应用筛选
        </el-button>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 实时观众趋势 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">实时观众趋势</h3>
          <el-select v-model="viewersPeriod" size="small" style="width: 120px">
            <el-option label="1小时" value="1h" />
            <el-option label="6小时" value="6h" />
            <el-option label="24小时" value="24h" />
          </el-select>
        </div>
        <div class="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
          <div class="text-center text-gray-500">
            <Activity class="w-12 h-12 mx-auto mb-2 text-gray-400" />
            <p>实时观众趋势图</p>
            <p class="text-sm">（图表组件待集成）</p>
          </div>
        </div>
      </div>

      <!-- 收益分析 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">收益分析</h3>
          <el-select v-model="incomePeriod" size="small" style="width: 120px">
            <el-option label="今日" value="today" />
            <el-option label="本周" value="week" />
            <el-option label="本月" value="month" />
          </el-select>
        </div>
        <div class="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
          <div class="text-center text-gray-500">
            <DollarSign class="w-12 h-12 mx-auto mb-2 text-gray-400" />
            <p>收益分析图表</p>
            <p class="text-sm">（图表组件待集成）</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 在线主播监控 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">在线主播监控</h3>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">{{ onlineStreamers.length }} 人在线</span>
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <el-table :data="onlineStreamers" style="width: 100%">
          <el-table-column label="主播信息" min-width="200">
            <template #default="{ row }">
              <div class="flex items-center space-x-3">
                <div class="relative">
                  <img
                    :src="row.avatar"
                    :alt="row.name"
                    class="w-10 h-10 rounded-full object-cover"
                  />
                  <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                </div>
                <div>
                  <p class="font-medium text-gray-900">{{ row.name }}</p>
                  <p class="text-sm text-gray-500">{{ row.guild }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="直播时长" width="120">
            <template #default="{ row }">
              <span class="text-sm">{{ row.duration }}</span>
            </template>
          </el-table-column>
          <el-table-column label="当前观众" width="120">
            <template #default="{ row }">
              <div class="flex items-center">
                <Users class="w-4 h-4 mr-1 text-blue-500" />
                <span class="font-medium">{{ formatNumber(row.viewers) }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="峰值观众" width="120">
            <template #default="{ row }">
              <span class="text-orange-600 font-medium">{{ formatNumber(row.peakViewers) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="今日收益" width="120">
            <template #default="{ row }">
              <span class="text-green-600 font-medium">¥{{ row.todayIncome }}</span>
            </template>
          </el-table-column>
          <el-table-column label="互动率" width="100">
            <template #default="{ row }">
              <el-progress
                :percentage="row.interactionRate"
                :color="getInteractionColor(row.interactionRate)"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="text-xs text-gray-500">{{ row.interactionRate }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <div class="flex space-x-1">
                <el-button size="small" @click="viewLiveRoom(row)">
                  <Eye class="w-4 h-4" />
                </el-button>
                <el-button size="small" type="primary" @click="viewDetails(row)">
                  <BarChart3 class="w-4 h-4" />
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 历史数据表格 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">历史数据记录</h3>
      </div>
      <div class="overflow-x-auto">
        <el-table :data="historicalData" style="width: 100%" v-loading="loading">
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="streamerName" label="主播" width="150" />
          <el-table-column prop="guild" label="工会" width="120" />
          <el-table-column label="直播时长" width="120">
            <template #default="{ row }">
              {{ row.duration }}
            </template>
          </el-table-column>
          <el-table-column label="观众数" width="120">
            <template #default="{ row }">
              <div>
                <p class="text-sm">平均: {{ formatNumber(row.avgViewers) }}</p>
                <p class="text-xs text-gray-500">峰值: {{ formatNumber(row.peakViewers) }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="收益" width="120">
            <template #default="{ row }">
              <span class="text-green-600 font-medium">¥{{ row.income }}</span>
            </template>
          </el-table-column>
          <el-table-column label="互动数据" width="150">
            <template #default="{ row }">
              <div class="text-sm">
                <p>点赞: {{ formatNumber(row.likes) }}</p>
                <p>评论: {{ formatNumber(row.comments) }}</p>
                <p>礼物: {{ formatNumber(row.gifts) }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button size="small" @click="exportData(row)">
                <Download class="w-4 h-4" />
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div class="p-4 border-t border-gray-200">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalRecords"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 主播详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="主播直播详情" width="800px">
      <div v-if="selectedStreamer" class="space-y-6">
        <!-- 基本信息 -->
        <div class="flex items-center space-x-4">
          <img
            :src="selectedStreamer.avatar"
            :alt="selectedStreamer.name"
            class="w-16 h-16 rounded-full object-cover"
          />
          <div>
            <h3 class="text-xl font-bold text-gray-900">{{ selectedStreamer.name }}</h3>
            <p class="text-gray-600">{{ selectedStreamer.guild }}</p>
            <p class="text-sm text-gray-500">直播时长: {{ selectedStreamer.duration }}</p>
          </div>
        </div>

        <!-- 实时数据 -->
        <div class="grid grid-cols-4 gap-4">
          <div class="bg-blue-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-blue-600">{{ formatNumber(selectedStreamer.viewers) }}</p>
            <p class="text-sm text-gray-600">当前观众</p>
          </div>
          <div class="bg-orange-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-orange-600">{{ formatNumber(selectedStreamer.peakViewers) }}</p>
            <p class="text-sm text-gray-600">峰值观众</p>
          </div>
          <div class="bg-green-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-green-600">¥{{ selectedStreamer.todayIncome }}</p>
            <p class="text-sm text-gray-600">今日收益</p>
          </div>
          <div class="bg-purple-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-purple-600">{{ selectedStreamer.interactionRate }}%</p>
            <p class="text-sm text-gray-600">互动率</p>
          </div>
        </div>

        <!-- 趋势图表占位 -->
        <div class="bg-gray-50 rounded-lg p-8 text-center">
          <Activity class="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <p class="text-gray-500">主播详细数据趋势图</p>
          <p class="text-sm text-gray-400">（图表组件待集成）</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Activity,
  Users,
  DollarSign,
  TrendingUp,
  TrendingDown,
  RotateCcw,
  RefreshCw,
  Filter,
  Eye,
  BarChart3,
  Download
} from 'lucide-vue-next'

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const refreshInterval = ref(null)
const dateRange = ref([])
const selectedGuild = ref('')
const dataType = ref('viewers')
const viewersPeriod = ref('1h')
const incomePeriod = ref('today')
const currentPage = ref(1)
const pageSize = ref(20)
const totalRecords = ref(0)
const showDetailDialog = ref(false)
const selectedStreamer = ref(null)

// 实时统计数据
const realTimeStats = ref([
  {
    title: '在线主播',
    value: '89',
    change: '+12',
    trend: 'up',
    icon: Users,
    bgColor: 'bg-blue-50',
    iconColor: 'text-blue-600'
  },
  {
    title: '总观众数',
    value: '45,280',
    change: '+8.5%',
    trend: 'up',
    icon: Activity,
    bgColor: 'bg-green-50',
    iconColor: 'text-green-600'
  },
  {
    title: '实时收益',
    value: '¥12,580',
    change: '+15.2%',
    trend: 'up',
    icon: DollarSign,
    bgColor: 'bg-yellow-50',
    iconColor: 'text-yellow-600'
  },
  {
    title: '平均互动率',
    value: '68%',
    change: '-2.1%',
    trend: 'down',
    icon: TrendingUp,
    bgColor: 'bg-purple-50',
    iconColor: 'text-purple-600'
  }
])

// 在线主播数据
const onlineStreamers = ref([
  {
    id: 'S001',
    name: '小美',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=beautiful%20female%20streamer%20avatar%20cartoon%20style&image_size=square',
    guild: '星光工会',
    duration: '3小时25分',
    viewers: 2580,
    peakViewers: 3420,
    todayIncome: 4580,
    interactionRate: 75,
    status: 'live'
  },
  {
    id: 'S002',
    name: '阳光少女',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=cute%20girl%20streamer%20avatar%20anime%20style&image_size=square',
    guild: '梦想工会',
    duration: '2小时15分',
    viewers: 1890,
    peakViewers: 2340,
    todayIncome: 3200,
    interactionRate: 68,
    status: 'live'
  },
  {
    id: 'S003',
    name: '游戏达人',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=gaming%20streamer%20avatar%20modern%20style&image_size=square',
    guild: '彩虹工会',
    duration: '4小时50分',
    viewers: 3420,
    peakViewers: 4680,
    todayIncome: 6800,
    interactionRate: 82,
    status: 'live'
  }
])

// 历史数据
const historicalData = ref([
  {
    date: '2024-01-15',
    streamerName: '小美',
    guild: '星光工会',
    duration: '6小时30分',
    avgViewers: 2200,
    peakViewers: 3800,
    income: 8500,
    likes: 15600,
    comments: 2340,
    gifts: 156
  },
  {
    date: '2024-01-14',
    streamerName: '阳光少女',
    guild: '梦想工会',
    duration: '5小时15分',
    avgViewers: 1800,
    peakViewers: 2600,
    income: 6200,
    likes: 12400,
    comments: 1890,
    gifts: 98
  },
  {
    date: '2024-01-13',
    streamerName: '游戏达人',
    guild: '彩虹工会',
    duration: '7小时20分',
    avgViewers: 3100,
    peakViewers: 4500,
    income: 12800,
    likes: 28900,
    comments: 4560,
    gifts: 234
  }
])

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

// 获取互动率颜色
const getInteractionColor = (rate: number) => {
  if (rate >= 80) return '#67c23a'
  if (rate >= 60) return '#e6a23c'
  return '#f56c6c'
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'live': return 'success'
    case 'break': return 'warning'
    case 'offline': return 'info'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'live': return '直播中'
    case 'break': return '休息中'
    case 'offline': return '离线'
    default: return '未知'
  }
}

// 切换自动刷新
const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  if (autoRefresh.value) {
    refreshInterval.value = setInterval(() => {
      refreshData()
    }, 30000) // 30秒刷新一次
    ElMessage.success('已开启自动刷新（30秒间隔）')
  } else {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }
    ElMessage.info('已停止自动刷新')
  }
}

// 手动刷新数据
const refreshData = () => {
  loading.value = true
  // 模拟API调用
  setTimeout(() => {
    // 更新实时数据
    realTimeStats.value.forEach(stat => {
      const randomChange = (Math.random() - 0.5) * 20
      stat.change = randomChange > 0 ? `+${randomChange.toFixed(1)}%` : `${randomChange.toFixed(1)}%`
      stat.trend = randomChange > 0 ? 'up' : 'down'
    })
    
    // 更新在线主播数据
    onlineStreamers.value.forEach(streamer => {
      streamer.viewers += Math.floor((Math.random() - 0.5) * 200)
      streamer.todayIncome += Math.floor(Math.random() * 100)
      streamer.interactionRate = Math.max(30, Math.min(95, streamer.interactionRate + Math.floor((Math.random() - 0.5) * 10)))
    })
    
    loading.value = false
    if (!autoRefresh.value) {
      ElMessage.success('数据刷新成功')
    }
  }, 1000)
}

// 应用筛选
const applyFilters = () => {
  loading.value = true
  // 模拟筛选API调用
  setTimeout(() => {
    loading.value = false
    ElMessage.success('筛选应用成功')
  }, 500)
}

// 查看直播间
const viewLiveRoom = (streamer: any) => {
  ElMessage.info(`打开 ${streamer.name} 的直播间`)
}

// 查看详情
const viewDetails = (streamer: any) => {
  selectedStreamer.value = streamer
  showDetailDialog.value = true
}

// 导出数据
const exportData = (record: any) => {
  ElMessage.success(`导出 ${record.streamerName} 在 ${record.date} 的数据`)
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 组件挂载和卸载
onMounted(() => {
  totalRecords.value = historicalData.value.length
  refreshData()
})

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>