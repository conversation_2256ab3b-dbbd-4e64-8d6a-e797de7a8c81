<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out" :class="{ '-translate-x-full': !sidebarOpen }">
      <div class="flex items-center justify-center h-16 bg-blue-600">
        <h1 class="text-xl font-bold text-white">工会主播管理平台</h1>
      </div>
      
      <nav class="mt-8">
        <div class="px-4 space-y-2">
          <router-link
            v-for="item in menuItems"
            :key="item.name"
            :to="item.path"
            class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-colors duration-200"
            :class="{ 'bg-blue-50 text-blue-600 border-r-2 border-blue-600': $route.name === item.name }"
          >
            <component :is="item.icon" class="w-5 h-5 mr-3" />
            <span class="font-medium">{{ item.label }}</span>
          </router-link>
        </div>
      </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="lg:pl-64">
      <!-- 顶部栏 -->
      <div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
        <button
          type="button"
          class="-m-2.5 p-2.5 text-gray-700 lg:hidden"
          @click="sidebarOpen = !sidebarOpen"
        >
          <Menu class="h-6 w-6" />
        </button>

        <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
          <div class="flex flex-1 items-center">
            <h2 class="text-lg font-semibold text-gray-900">{{ currentPageTitle }}</h2>
          </div>
          
          <div class="flex items-center gap-x-4 lg:gap-x-6">
            <!-- 用户菜单 -->
            <el-dropdown trigger="click">
              <div class="flex items-center cursor-pointer">
                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-medium">{{ userInitial }}</span>
                </div>
                <span class="ml-2 text-sm font-medium text-gray-700">{{ authStore.user?.name }}</span>
                <ChevronDown class="ml-1 w-4 h-4 text-gray-500" />
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleLogout">
                    <LogOut class="w-4 h-4 mr-2" />
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <!-- 页面内容 -->
      <main class="py-6">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import {
  LayoutDashboard,
  Users,
  Building2,
  BarChart3,
  DollarSign,
  Settings,
  Menu,
  ChevronDown,
  LogOut
} from 'lucide-vue-next'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const sidebarOpen = ref(false)

// 菜单项配置
const menuItems = computed(() => {
  const items = [
    {
      name: 'dashboard',
      path: '/',
      label: '仪表板',
      icon: LayoutDashboard
    },
    {
      name: 'streamers',
      path: '/streamers',
      label: '主播管理',
      icon: Users
    },
    {
      name: 'live-data',
      path: '/live-data',
      label: '直播数据',
      icon: BarChart3
    },
    {
      name: 'earnings',
      path: '/earnings',
      label: '收益统计',
      icon: DollarSign
    },
    {
      name: 'settings',
      path: '/settings',
      label: '设置',
      icon: Settings
    }
  ]

  // 只有运营人员和管理员可以看到工会管理
  if (authStore.isOperator || authStore.isAdmin) {
    items.splice(2, 0, {
      name: 'guilds',
      path: '/guilds',
      label: '工会管理',
      icon: Building2
    })
  }

  return items
})

// 当前页面标题
const currentPageTitle = computed(() => {
  const currentItem = menuItems.value.find(item => item.name === route.name)
  return currentItem?.label || '工会主播管理平台'
})

// 用户名首字母
const userInitial = computed(() => {
  return authStore.user?.name?.charAt(0).toUpperCase() || 'U'
})

// 退出登录
const handleLogout = () => {
  authStore.logout()
  ElMessage.success('已退出登录')
  router.push('/login')
}
</script>

<style scoped>
.router-link-active {
  @apply bg-blue-50 text-blue-600;
}
</style>