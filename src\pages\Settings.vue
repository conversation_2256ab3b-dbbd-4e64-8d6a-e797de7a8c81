<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">系统设置</h1>
      <p class="text-gray-600 mt-1">管理系统配置和个人偏好设置</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- 设置导航 -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <nav class="space-y-2">
            <button
              v-for="tab in settingTabs"
              :key="tab.key"
              @click="activeTab = tab.key"
              :class="[
                'w-full flex items-center px-3 py-2 text-left rounded-lg transition-colors duration-200',
                activeTab === tab.key
                  ? 'bg-blue-50 text-blue-700 border border-blue-200'
                  : 'text-gray-600 hover:bg-gray-50'
              ]"
            >
              <component :is="tab.icon" class="w-5 h-5 mr-3" />
              {{ tab.label }}
            </button>
          </nav>
        </div>
      </div>

      <!-- 设置内容 -->
      <div class="lg:col-span-3">
        <!-- 个人信息设置 -->
        <div v-if="activeTab === 'profile'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">个人信息</h3>
          <el-form :model="profileForm" label-width="120px" class="max-w-2xl">
            <el-form-item label="头像">
              <div class="flex items-center space-x-4">
                <img
                  :src="profileForm.avatar"
                  alt="头像"
                  class="w-16 h-16 rounded-full object-cover border border-gray-200"
                />
                <div>
                  <el-button size="small" @click="uploadAvatar">更换头像</el-button>
                  <p class="text-sm text-gray-500 mt-1">支持 JPG、PNG 格式，建议尺寸 200x200</p>
                </div>
              </div>
            </el-form-item>
            <el-form-item label="姓名">
              <el-input v-model="profileForm.name" style="width: 300px" />
            </el-form-item>
            <el-form-item label="邮箱">
              <el-input v-model="profileForm.email" style="width: 300px" />
            </el-form-item>
            <el-form-item label="手机号">
              <el-input v-model="profileForm.phone" style="width: 300px" />
            </el-form-item>
            <el-form-item label="职位">
              <el-input v-model="profileForm.position" style="width: 300px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveProfile">保存更改</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 安全设置 -->
        <div v-if="activeTab === 'security'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">安全设置</h3>
          <div class="space-y-6 max-w-2xl">
            <!-- 修改密码 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-4">修改密码</h4>
              <el-form :model="passwordForm" label-width="120px">
                <el-form-item label="当前密码">
                  <el-input
                    v-model="passwordForm.currentPassword"
                    type="password"
                    show-password
                    style="width: 300px"
                  />
                </el-form-item>
                <el-form-item label="新密码">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    show-password
                    style="width: 300px"
                  />
                </el-form-item>
                <el-form-item label="确认密码">
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    show-password
                    style="width: 300px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="changePassword">更新密码</el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 两步验证 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <h4 class="font-medium text-gray-900">两步验证</h4>
                  <p class="text-sm text-gray-500">为您的账户添加额外的安全保护</p>
                </div>
                <el-switch
                  v-model="securitySettings.twoFactorEnabled"
                  @change="toggleTwoFactor"
                />
              </div>
            </div>

            <!-- 登录历史 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-4">最近登录记录</h4>
              <div class="space-y-3">
                <div
                  v-for="login in loginHistory"
                  :key="login.id"
                  class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
                >
                  <div class="flex items-center space-x-3">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div>
                      <p class="text-sm font-medium text-gray-900">{{ login.location }}</p>
                      <p class="text-xs text-gray-500">{{ login.device }}</p>
                    </div>
                  </div>
                  <div class="text-right">
                    <p class="text-sm text-gray-900">{{ login.time }}</p>
                    <p class="text-xs text-gray-500">{{ login.ip }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 通知设置 -->
        <div v-if="activeTab === 'notifications'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">通知设置</h3>
          <div class="space-y-6 max-w-2xl">
            <div
              v-for="notification in notificationSettings"
              :key="notification.key"
              class="flex items-center justify-between py-4 border-b border-gray-200 last:border-b-0"
            >
              <div>
                <h4 class="font-medium text-gray-900">{{ notification.title }}</h4>
                <p class="text-sm text-gray-500">{{ notification.description }}</p>
              </div>
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600">邮件</span>
                  <el-switch v-model="notification.email" size="small" />
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600">短信</span>
                  <el-switch v-model="notification.sms" size="small" />
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-600">推送</span>
                  <el-switch v-model="notification.push" size="small" />
                </div>
              </div>
            </div>
            <div class="pt-4">
              <el-button type="primary" @click="saveNotifications">保存设置</el-button>
            </div>
          </div>
        </div>

        <!-- 系统配置 -->
        <div v-if="activeTab === 'system'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">系统配置</h3>
          <div class="space-y-6 max-w-2xl">
            <!-- 平台设置 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-4">平台设置</h4>
              <el-form :model="systemConfig" label-width="150px">
                <el-form-item label="平台名称">
                  <el-input v-model="systemConfig.platformName" style="width: 300px" />
                </el-form-item>
                <el-form-item label="默认分成比例">
                  <el-input-number
                    v-model="systemConfig.defaultCommission"
                    :min="0"
                    :max="100"
                    :precision="1"
                    style="width: 300px"
                  />
                  <span class="ml-2 text-sm text-gray-500">%</span>
                </el-form-item>
                <el-form-item label="最低提现金额">
                  <el-input-number
                    v-model="systemConfig.minWithdraw"
                    :min="1"
                    style="width: 300px"
                  />
                  <span class="ml-2 text-sm text-gray-500">元</span>
                </el-form-item>
                <el-form-item label="结算周期">
                  <el-select v-model="systemConfig.settlementCycle" style="width: 300px">
                    <el-option label="每日结算" value="daily" />
                    <el-option label="每周结算" value="weekly" />
                    <el-option label="每月结算" value="monthly" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>

            <!-- 功能开关 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-4">功能开关</h4>
              <div class="space-y-4">
                <div
                  v-for="feature in systemFeatures"
                  :key="feature.key"
                  class="flex items-center justify-between"
                >
                  <div>
                    <p class="font-medium text-gray-900">{{ feature.name }}</p>
                    <p class="text-sm text-gray-500">{{ feature.description }}</p>
                  </div>
                  <el-switch v-model="feature.enabled" />
                </div>
              </div>
            </div>

            <div class="pt-4">
              <el-button type="primary" @click="saveSystemConfig">保存配置</el-button>
            </div>
          </div>
        </div>

        <!-- 数据管理 -->
        <div v-if="activeTab === 'data'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">数据管理</h3>
          <div class="space-y-6 max-w-2xl">
            <!-- 数据备份 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-4">数据备份</h4>
              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="font-medium text-gray-900">自动备份</p>
                    <p class="text-sm text-gray-500">每日自动备份系统数据</p>
                  </div>
                  <el-switch v-model="dataSettings.autoBackup" />
                </div>
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-gray-600">上次备份时间</p>
                    <p class="text-sm font-medium">{{ dataSettings.lastBackup }}</p>
                  </div>
                  <el-button size="small" @click="createBackup">立即备份</el-button>
                </div>
              </div>
            </div>

            <!-- 数据导出 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-4">数据导出</h4>
              <div class="grid grid-cols-2 gap-4">
                <el-button @click="exportData('users')">导出用户数据</el-button>
                <el-button @click="exportData('earnings')">导出收益数据</el-button>
                <el-button @click="exportData('guilds')">导出工会数据</el-button>
                <el-button @click="exportData('live')">导出直播数据</el-button>
              </div>
            </div>

            <!-- 数据清理 -->
            <div class="border border-red-200 rounded-lg p-4 bg-red-50">
              <h4 class="font-medium text-red-900 mb-4">数据清理</h4>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-red-900">清理过期日志</p>
                    <p class="text-xs text-red-600">删除30天前的系统日志</p>
                  </div>
                  <el-button size="small" type="danger" @click="cleanData('logs')">清理</el-button>
                </div>
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-red-900">清理临时文件</p>
                    <p class="text-xs text-red-600">删除系统临时文件</p>
                  </div>
                  <el-button size="small" type="danger" @click="cleanData('temp')">清理</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 关于系统 -->
        <div v-if="activeTab === 'about'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-6">关于系统</h3>
          <div class="space-y-6 max-w-2xl">
            <!-- 系统信息 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-4">系统信息</h4>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-gray-600">系统名称</span>
                  <span class="font-medium">工会主播管理平台</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">版本号</span>
                  <span class="font-medium">v1.0.0</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">发布日期</span>
                  <span class="font-medium">2024-01-15</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">技术栈</span>
                  <span class="font-medium">Vue 3 + TypeScript + Element Plus</span>
                </div>
              </div>
            </div>

            <!-- 更新日志 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-4">更新日志</h4>
              <div class="space-y-4">
                <div
                  v-for="update in updateLogs"
                  :key="update.version"
                  class="border-l-4 border-blue-500 pl-4"
                >
                  <div class="flex items-center space-x-2 mb-2">
                    <span class="font-medium text-gray-900">{{ update.version }}</span>
                    <span class="text-sm text-gray-500">{{ update.date }}</span>
                  </div>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li v-for="change in update.changes" :key="change" class="flex items-start">
                      <span class="w-1 h-1 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                      {{ change }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- 联系信息 -->
            <div class="border border-gray-200 rounded-lg p-4">
              <h4 class="font-medium text-gray-900 mb-4">技术支持</h4>
              <div class="space-y-2">
                <p class="text-sm text-gray-600">
                  <span class="font-medium">邮箱：</span><EMAIL>
                </p>
                <p class="text-sm text-gray-600">
                  <span class="font-medium">电话：</span>400-123-4567
                </p>
                <p class="text-sm text-gray-600">
                  <span class="font-medium">工作时间：</span>周一至周五 9:00-18:00
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User,
  Shield,
  Bell,
  Settings,
  Database,
  Info
} from 'lucide-vue-next'

// 当前激活的标签
const activeTab = ref('profile')

// 设置标签
const settingTabs = [
  { key: 'profile', label: '个人信息', icon: User },
  { key: 'security', label: '安全设置', icon: Shield },
  { key: 'notifications', label: '通知设置', icon: Bell },
  { key: 'system', label: '系统配置', icon: Settings },
  { key: 'data', label: '数据管理', icon: Database },
  { key: 'about', label: '关于系统', icon: Info }
]

// 个人信息表单
const profileForm = ref({
  avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20manager%20avatar%20business%20style&image_size=square',
  name: '管理员',
  email: '<EMAIL>',
  phone: '***********',
  position: '系统管理员'
})

// 密码表单
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 安全设置
const securitySettings = ref({
  twoFactorEnabled: false
})

// 登录历史
const loginHistory = ref([
  {
    id: 1,
    location: '北京市',
    device: 'Chrome 浏览器',
    time: '2024-01-15 14:30',
    ip: '*************'
  },
  {
    id: 2,
    location: '上海市',
    device: 'Safari 浏览器',
    time: '2024-01-14 09:15',
    ip: '*************'
  },
  {
    id: 3,
    location: '广州市',
    device: 'Firefox 浏览器',
    time: '2024-01-13 16:45',
    ip: '*************'
  }
])

// 通知设置
const notificationSettings = ref([
  {
    key: 'new_streamer',
    title: '新主播注册',
    description: '有新主播注册时通知我',
    email: true,
    sms: false,
    push: true
  },
  {
    key: 'earnings_alert',
    title: '收益异常',
    description: '收益出现异常波动时通知我',
    email: true,
    sms: true,
    push: true
  },
  {
    key: 'system_maintenance',
    title: '系统维护',
    description: '系统维护和更新通知',
    email: true,
    sms: false,
    push: false
  },
  {
    key: 'settlement_reminder',
    title: '结算提醒',
    description: '结算周期到期提醒',
    email: true,
    sms: true,
    push: true
  }
])

// 系统配置
const systemConfig = ref({
  platformName: '工会主播管理平台',
  defaultCommission: 50.0,
  minWithdraw: 100,
  settlementCycle: 'weekly'
})

// 系统功能
const systemFeatures = ref([
  {
    key: 'auto_settlement',
    name: '自动结算',
    description: '启用自动结算功能',
    enabled: true
  },
  {
    key: 'real_time_monitoring',
    name: '实时监控',
    description: '启用实时数据监控',
    enabled: true
  },
  {
    key: 'data_analytics',
    name: '数据分析',
    description: '启用高级数据分析功能',
    enabled: false
  },
  {
    key: 'mobile_app',
    name: '移动端支持',
    description: '启用移动端应用支持',
    enabled: false
  }
])

// 数据设置
const dataSettings = ref({
  autoBackup: true,
  lastBackup: '2024-01-15 03:00:00'
})

// 更新日志
const updateLogs = ref([
  {
    version: 'v1.0.0',
    date: '2024-01-15',
    changes: [
      '初始版本发布',
      '完成主播管理功能',
      '完成工会管理功能',
      '完成收益统计功能',
      '完成直播数据监控功能'
    ]
  },
  {
    version: 'v0.9.0',
    date: '2024-01-10',
    changes: [
      '完成用户认证系统',
      '完成基础页面布局',
      '完成路由配置',
      '完成状态管理'
    ]
  }
])

// 上传头像
const uploadAvatar = () => {
  ElMessage.info('头像上传功能待实现')
}

// 保存个人信息
const saveProfile = () => {
  ElMessage.success('个人信息保存成功')
}

// 修改密码
const changePassword = async () => {
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    ElMessage.error('两次输入的密码不一致')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      '确定要修改密码吗？修改后需要重新登录。',
      '确认修改',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟API调用
    ElMessage.success('密码修改成功，请重新登录')
    
    // 重置表单
    passwordForm.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  } catch {
    // 用户取消操作
  }
}

// 切换两步验证
const toggleTwoFactor = (enabled: boolean) => {
  if (enabled) {
    ElMessage.success('两步验证已启用')
  } else {
    ElMessage.info('两步验证已关闭')
  }
}

// 保存通知设置
const saveNotifications = () => {
  ElMessage.success('通知设置保存成功')
}

// 保存系统配置
const saveSystemConfig = () => {
  ElMessage.success('系统配置保存成功')
}

// 创建备份
const createBackup = async () => {
  try {
    ElMessage.info('正在创建备份...')
    // 模拟备份过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    dataSettings.value.lastBackup = new Date().toLocaleString('zh-CN')
    ElMessage.success('数据备份创建成功')
  } catch (error) {
    ElMessage.error('备份创建失败')
  }
}

// 导出数据
const exportData = (type: string) => {
  const typeMap: Record<string, string> = {
    users: '用户',
    earnings: '收益',
    guilds: '工会',
    live: '直播'
  }
  ElMessage.success(`${typeMap[type]}数据导出成功`)
}

// 清理数据
const cleanData = async (type: string) => {
  const typeMap: Record<string, string> = {
    logs: '过期日志',
    temp: '临时文件'
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要清理${typeMap[type]}吗？此操作不可恢复。`,
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success(`${typeMap[type]}清理成功`)
  } catch {
    // 用户取消操作
  }
}
</script>