<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">收益统计</h1>
      <p class="text-gray-600 mt-1">收益分析、财务报表和结算管理</p>
    </div>

    <!-- 收益概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div
        v-for="stat in earningsStats"
        :key="stat.title"
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">{{ stat.title }}</p>
            <p class="text-3xl font-bold text-gray-900 mt-2">{{ stat.value }}</p>
            <div class="flex items-center mt-2">
              <component
                :is="stat.trend === 'up' ? TrendingUp : TrendingDown"
                :class="[
                  'w-4 h-4 mr-1',
                  stat.trend === 'up' ? 'text-green-500' : 'text-red-500'
                ]"
              />
              <span
                :class="[
                  'text-sm font-medium',
                  stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                ]"
              >
                {{ stat.change }}
              </span>
              <span class="text-sm text-gray-500 ml-1">vs 上月</span>
            </div>
          </div>
          <div
            :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center',
              stat.bgColor
            ]"
          >
            <component :is="stat.icon" :class="['w-6 h-6', stat.iconColor]" />
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和导出 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">数据筛选</h3>
        <div class="flex items-center space-x-2">
          <el-button type="primary" @click="exportReport">
            <Download class="w-4 h-4 mr-1" />
            导出报表
          </el-button>
          <el-button @click="generateReport">
            <FileText class="w-4 h-4 mr-1" />
            生成报告
          </el-button>
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <el-date-picker
          v-model="dateRange"
          type="monthrange"
          range-separator="至"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          format="YYYY-MM"
          value-format="YYYY-MM"
        />
        <el-select v-model="selectedGuild" placeholder="选择工会" clearable>
          <el-option label="全部工会" value="" />
          <el-option label="星光工会" value="1" />
          <el-option label="梦想工会" value="2" />
          <el-option label="彩虹工会" value="3" />
        </el-select>
        <el-select v-model="earningsType" placeholder="收益类型">
          <el-option label="全部收益" value="all" />
          <el-option label="礼物收益" value="gifts" />
          <el-option label="打赏收益" value="tips" />
          <el-option label="广告收益" value="ads" />
        </el-select>
        <el-select v-model="settlementStatus" placeholder="结算状态">
          <el-option label="全部状态" value="" />
          <el-option label="已结算" value="settled" />
          <el-option label="待结算" value="pending" />
          <el-option label="处理中" value="processing" />
        </el-select>
        <el-button type="primary" @click="applyFilters">
          <Filter class="w-4 h-4 mr-1" />
          应用筛选
        </el-button>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 收益趋势图 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">收益趋势</h3>
          <el-select v-model="trendPeriod" size="small" style="width: 120px">
            <el-option label="近7天" value="7d" />
            <el-option label="近30天" value="30d" />
            <el-option label="近90天" value="90d" />
          </el-select>
        </div>
        <div class="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
          <div class="text-center text-gray-500">
            <TrendingUp class="w-12 h-12 mx-auto mb-2 text-gray-400" />
            <p>收益趋势图表</p>
            <p class="text-sm">（图表组件待集成）</p>
          </div>
        </div>
      </div>

      <!-- 收益分布 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900">收益分布</h3>
          <el-select v-model="distributionType" size="small" style="width: 120px">
            <el-option label="按工会" value="guild" />
            <el-option label="按主播" value="streamer" />
            <el-option label="按类型" value="type" />
          </el-select>
        </div>
        <div class="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
          <div class="text-center text-gray-500">
            <PieChart class="w-12 h-12 mx-auto mb-2 text-gray-400" />
            <p>收益分布图表</p>
            <p class="text-sm">（图表组件待集成）</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 工会收益排行 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">工会收益排行</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div
            v-for="(guild, index) in guildRankings"
            :key="guild.id"
            class="relative bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200"
          >
            <!-- 排名徽章 -->
            <div
              :class="[
                'absolute -top-2 -right-2 w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm',
                index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : 'bg-orange-500'
              ]"
            >
              {{ index + 1 }}
            </div>
            
            <div class="flex items-center space-x-4">
              <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                <Building class="w-6 h-6 text-white" />
              </div>
              <div class="flex-1">
                <h4 class="font-semibold text-gray-900">{{ guild.name }}</h4>
                <p class="text-sm text-gray-600">{{ guild.memberCount }} 名成员</p>
              </div>
            </div>
            
            <div class="mt-4 space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">本月收益</span>
                <span class="font-bold text-green-600">¥{{ formatNumber(guild.monthlyEarnings) }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">总收益</span>
                <span class="font-semibold text-gray-900">¥{{ formatNumber(guild.totalEarnings) }}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">增长率</span>
                <span
                  :class="[
                    'font-medium',
                    guild.growthRate > 0 ? 'text-green-600' : 'text-red-600'
                  ]"
                >
                  {{ guild.growthRate > 0 ? '+' : '' }}{{ guild.growthRate }}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主播收益明细 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900">主播收益明细</h3>
          <div class="flex items-center space-x-2">
            <el-input
              v-model="searchQuery"
              placeholder="搜索主播"
              size="small"
              style="width: 200px"
              clearable
            >
              <template #prefix>
                <Search class="w-4 h-4 text-gray-400" />
              </template>
            </el-input>
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <el-table :data="filteredEarningsData" style="width: 100%" v-loading="loading">
          <el-table-column label="主播信息" min-width="200">
            <template #default="{ row }">
              <div class="flex items-center space-x-3">
                <img
                  :src="row.avatar"
                  :alt="row.name"
                  class="w-10 h-10 rounded-full object-cover"
                />
                <div>
                  <p class="font-medium text-gray-900">{{ row.name }}</p>
                  <p class="text-sm text-gray-500">{{ row.guild }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="level" label="等级" width="80" />
          <el-table-column label="今日收益" width="120">
            <template #default="{ row }">
              <span class="font-medium text-green-600">¥{{ row.todayEarnings }}</span>
            </template>
          </el-table-column>
          <el-table-column label="本月收益" width="120">
            <template #default="{ row }">
              <span class="font-medium text-blue-600">¥{{ formatNumber(row.monthlyEarnings) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总收益" width="120">
            <template #default="{ row }">
              <span class="font-medium text-gray-900">¥{{ formatNumber(row.totalEarnings) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="分成比例" width="100">
            <template #default="{ row }">
              <span class="text-sm">{{ row.commissionRate }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="待结算" width="120">
            <template #default="{ row }">
              <span class="font-medium text-orange-600">¥{{ row.pendingSettlement }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结算状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getSettlementType(row.settlementStatus)" size="small">
                {{ getSettlementText(row.settlementStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <div class="flex space-x-1">
                <el-button size="small" @click="viewEarningsDetail(row)">
                  <Eye class="w-4 h-4" />
                </el-button>
                <el-button size="small" type="primary" @click="processSettlement(row)">
                  <Calculator class="w-4 h-4" />
                </el-button>
                <el-button size="small" @click="exportStreamerData(row)">
                  <Download class="w-4 h-4" />
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div class="p-4 border-t border-gray-200">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalEarningsRecords"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 收益详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="收益详情" width="800px">
      <div v-if="selectedStreamer" class="space-y-6">
        <!-- 主播基本信息 -->
        <div class="flex items-center space-x-4">
          <img
            :src="selectedStreamer.avatar"
            :alt="selectedStreamer.name"
            class="w-16 h-16 rounded-full object-cover"
          />
          <div>
            <h3 class="text-xl font-bold text-gray-900">{{ selectedStreamer.name }}</h3>
            <p class="text-gray-600">{{ selectedStreamer.guild }} · 等级 {{ selectedStreamer.level }}</p>
            <p class="text-sm text-gray-500">分成比例: {{ selectedStreamer.commissionRate }}%</p>
          </div>
        </div>

        <!-- 收益统计 -->
        <div class="grid grid-cols-4 gap-4">
          <div class="bg-green-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-green-600">¥{{ selectedStreamer.todayEarnings }}</p>
            <p class="text-sm text-gray-600">今日收益</p>
          </div>
          <div class="bg-blue-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-blue-600">¥{{ formatNumber(selectedStreamer.monthlyEarnings) }}</p>
            <p class="text-sm text-gray-600">本月收益</p>
          </div>
          <div class="bg-purple-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-purple-600">¥{{ formatNumber(selectedStreamer.totalEarnings) }}</p>
            <p class="text-sm text-gray-600">总收益</p>
          </div>
          <div class="bg-orange-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-orange-600">¥{{ selectedStreamer.pendingSettlement }}</p>
            <p class="text-sm text-gray-600">待结算</p>
          </div>
        </div>

        <!-- 收益明细表格 -->
        <div>
          <h4 class="font-semibold text-gray-900 mb-4">收益明细</h4>
          <el-table :data="earningsDetails" style="width: 100%" size="small">
            <el-table-column prop="date" label="日期" width="120" />
            <el-table-column prop="type" label="类型" width="100" />
            <el-table-column label="金额" width="120">
              <template #default="{ row }">
                <span class="text-green-600 font-medium">¥{{ row.amount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="source" label="来源" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getSettlementType(row.status)" size="small">
                  {{ getSettlementText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 结算处理对话框 -->
    <el-dialog v-model="showSettlementDialog" title="结算处理" width="500px">
      <div v-if="settlementStreamer">
        <div class="space-y-4">
          <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="font-semibold text-gray-900 mb-2">结算信息</h4>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-600">主播姓名</span>
                <span class="font-medium">{{ settlementStreamer.name }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">待结算金额</span>
                <span class="font-bold text-orange-600">¥{{ settlementStreamer.pendingSettlement }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">分成比例</span>
                <span>{{ settlementStreamer.commissionRate }}%</span>
              </div>
            </div>
          </div>
          
          <el-form :model="settlementForm" label-width="100px">
            <el-form-item label="结算金额">
              <el-input-number
                v-model="settlementForm.amount"
                :min="0"
                :max="settlementStreamer.pendingSettlement"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="结算方式">
              <el-select v-model="settlementForm.method" style="width: 100%">
                <el-option label="银行转账" value="bank" />
                <el-option label="支付宝" value="alipay" />
                <el-option label="微信" value="wechat" />
              </el-select>
            </el-form-item>
            <el-form-item label="备注">
              <el-input
                v-model="settlementForm.note"
                type="textarea"
                :rows="3"
                placeholder="请输入结算备注"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showSettlementDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmSettlement" :loading="processing">
            确认结算
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Users,
  Calculator,
  Download,
  FileText,
  Filter,
  Search,
  Eye,
  Building,
  PieChart
} from 'lucide-vue-next'

// 响应式数据
const loading = ref(false)
const processing = ref(false)
const dateRange = ref([])
const selectedGuild = ref('')
const earningsType = ref('all')
const settlementStatus = ref('')
const trendPeriod = ref('30d')
const distributionType = ref('guild')
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalEarningsRecords = ref(0)
const showDetailDialog = ref(false)
const showSettlementDialog = ref(false)
const selectedStreamer = ref(null)
const settlementStreamer = ref(null)

// 结算表单
const settlementForm = ref({
  amount: 0,
  method: 'bank',
  note: ''
})

// 收益统计数据
const earningsStats = ref([
  {
    title: '今日收益',
    value: '¥45,280',
    change: '+12.5%',
    trend: 'up',
    icon: DollarSign,
    bgColor: 'bg-green-50',
    iconColor: 'text-green-600'
  },
  {
    title: '本月收益',
    value: '¥1,234,567',
    change: '+8.3%',
    trend: 'up',
    icon: TrendingUp,
    bgColor: 'bg-blue-50',
    iconColor: 'text-blue-600'
  },
  {
    title: '待结算',
    value: '¥89,450',
    change: '-5.2%',
    trend: 'down',
    icon: Calculator,
    bgColor: 'bg-orange-50',
    iconColor: 'text-orange-600'
  },
  {
    title: '活跃主播',
    value: '156',
    change: '+15',
    trend: 'up',
    icon: Users,
    bgColor: 'bg-purple-50',
    iconColor: 'text-purple-600'
  }
])

// 工会排行数据
const guildRankings = ref([
  {
    id: 'G001',
    name: '星光工会',
    memberCount: 68,
    monthlyEarnings: 580900,
    totalEarnings: 2580900,
    growthRate: 15.2
  },
  {
    id: 'G002',
    name: '梦想工会',
    memberCount: 45,
    monthlyEarnings: 420600,
    totalEarnings: 1680200,
    growthRate: 8.7
  },
  {
    id: 'G003',
    name: '彩虹工会',
    memberCount: 43,
    monthlyEarnings: 380200,
    totalEarnings: 1520200,
    growthRate: -2.1
  }
])

// 主播收益数据
const earningsData = ref([
  {
    id: 'S001',
    name: '小美',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=beautiful%20female%20streamer%20avatar%20cartoon%20style&image_size=square',
    guild: '星光工会',
    level: 15,
    todayEarnings: 2580,
    monthlyEarnings: 68900,
    totalEarnings: 256800,
    commissionRate: 60,
    pendingSettlement: 12500,
    settlementStatus: 'pending'
  },
  {
    id: 'S002',
    name: '阳光少女',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=cute%20girl%20streamer%20avatar%20anime%20style&image_size=square',
    guild: '梦想工会',
    level: 12,
    todayEarnings: 1890,
    monthlyEarnings: 45600,
    totalEarnings: 189000,
    commissionRate: 55,
    pendingSettlement: 8900,
    settlementStatus: 'processing'
  },
  {
    id: 'S003',
    name: '游戏达人',
    avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=gaming%20streamer%20avatar%20modern%20style&image_size=square',
    guild: '彩虹工会',
    level: 20,
    todayEarnings: 3420,
    monthlyEarnings: 89200,
    totalEarnings: 342000,
    commissionRate: 65,
    pendingSettlement: 15600,
    settlementStatus: 'settled'
  }
])

// 收益明细数据
const earningsDetails = ref([
  {
    date: '2024-01-15',
    type: '礼物',
    amount: 1580,
    source: '直播间礼物',
    status: 'settled'
  },
  {
    date: '2024-01-14',
    type: '打赏',
    amount: 890,
    source: '观众打赏',
    status: 'pending'
  },
  {
    date: '2024-01-13',
    type: '广告',
    amount: 450,
    source: '广告分成',
    status: 'processing'
  }
])

// 过滤后的收益数据
const filteredEarningsData = computed(() => {
  let result = earningsData.value

  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(streamer => 
      streamer.name.includes(searchQuery.value)
    )
  }

  // 工会过滤
  if (selectedGuild.value) {
    const guildMap: Record<string, string> = {
      '1': '星光工会',
      '2': '梦想工会',
      '3': '彩虹工会'
    }
    result = result.filter(streamer => streamer.guild === guildMap[selectedGuild.value])
  }

  // 结算状态过滤
  if (settlementStatus.value) {
    result = result.filter(streamer => streamer.settlementStatus === settlementStatus.value)
  }

  totalEarningsRecords.value = result.length
  return result
})

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

// 获取结算状态类型
const getSettlementType = (status: string) => {
  switch (status) {
    case 'settled': return 'success'
    case 'processing': return 'warning'
    case 'pending': return 'info'
    default: return 'info'
  }
}

// 获取结算状态文本
const getSettlementText = (status: string) => {
  switch (status) {
    case 'settled': return '已结算'
    case 'processing': return '处理中'
    case 'pending': return '待结算'
    default: return '未知'
  }
}

// 应用筛选
const applyFilters = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('筛选应用成功')
  }, 500)
}

// 导出报表
const exportReport = () => {
  ElMessage.success('报表导出成功')
}

// 生成报告
const generateReport = () => {
  ElMessage.success('报告生成成功')
}

// 查看收益详情
const viewEarningsDetail = (streamer: any) => {
  selectedStreamer.value = streamer
  showDetailDialog.value = true
}

// 处理结算
const processSettlement = (streamer: any) => {
  settlementStreamer.value = streamer
  settlementForm.value.amount = streamer.pendingSettlement
  showSettlementDialog.value = true
}

// 确认结算
const confirmSettlement = async () => {
  processing.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 更新结算状态
    settlementStreamer.value.settlementStatus = 'processing'
    settlementStreamer.value.pendingSettlement -= settlementForm.value.amount
    
    ElMessage.success('结算处理成功')
    showSettlementDialog.value = false
  } catch (error) {
    ElMessage.error('结算处理失败')
  } finally {
    processing.value = false
  }
}

// 导出主播数据
const exportStreamerData = (streamer: any) => {
  ElMessage.success(`导出 ${streamer.name} 的收益数据`)
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 初始化
totalEarningsRecords.value = earningsData.value.length
</script>