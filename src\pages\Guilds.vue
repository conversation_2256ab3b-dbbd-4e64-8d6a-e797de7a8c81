<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">工会管理</h1>
        <p class="text-gray-600 mt-1">管理平台所有工会信息和成员</p>
      </div>
      <el-button type="primary" @click="showAddDialog = true">
        <Building class="w-4 h-4 mr-2" />
        创建工会
      </el-button>
    </div>

    <!-- 工会统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div
        v-for="stat in guildStats"
        :key="stat.title"
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">{{ stat.title }}</p>
            <p class="text-3xl font-bold text-gray-900 mt-2">{{ stat.value }}</p>
          </div>
          <div
            :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center',
              stat.bgColor
            ]"
          >
            <component :is="stat.icon" :class="['w-6 h-6', stat.iconColor]" />
          </div>
        </div>
      </div>
    </div>

    <!-- 工会列表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <div
        v-for="guild in guilds"
        :key="guild.id"
        class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200"
      >
        <!-- 工会头部 -->
        <div class="relative h-32 bg-gradient-to-r from-blue-500 to-purple-600">
          <div class="absolute inset-0 bg-black bg-opacity-20"></div>
          <div class="absolute bottom-4 left-4 text-white">
            <h3 class="text-xl font-bold">{{ guild.name }}</h3>
            <p class="text-blue-100">{{ guild.description }}</p>
          </div>
          <div class="absolute top-4 right-4">
            <el-dropdown trigger="click">
              <el-button type="primary" size="small" circle>
                <MoreVertical class="w-4 h-4" />
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="viewGuild(guild)">
                    <Eye class="w-4 h-4 mr-2" />
                    查看详情
                  </el-dropdown-item>
                  <el-dropdown-item @click="editGuild(guild)">
                    <Edit class="w-4 h-4 mr-2" />
                    编辑工会
                  </el-dropdown-item>
                  <el-dropdown-item @click="manageMembers(guild)">
                    <Users class="w-4 h-4 mr-2" />
                    成员管理
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="deleteGuild(guild)">
                    <Trash2 class="w-4 h-4 mr-2" />
                    删除工会
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <!-- 工会信息 -->
        <div class="p-6">
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="text-center">
              <p class="text-2xl font-bold text-blue-600">{{ guild.memberCount }}</p>
              <p class="text-sm text-gray-500">成员数量</p>
            </div>
            <div class="text-center">
              <p class="text-2xl font-bold text-green-600">¥{{ formatNumber(guild.totalIncome) }}</p>
              <p class="text-sm text-gray-500">总收益</p>
            </div>
          </div>
          
          <div class="space-y-2">
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-500">会长</span>
              <span class="font-medium">{{ guild.leader }}</span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-500">创建时间</span>
              <span>{{ formatDate(guild.createdAt) }}</span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-gray-500">状态</span>
              <el-tag :type="guild.status === 'active' ? 'success' : 'warning'" size="small">
                {{ guild.status === 'active' ? '活跃' : '暂停' }}
              </el-tag>
            </div>
          </div>

          <!-- 最近活跃成员 -->
          <div class="mt-4">
            <p class="text-sm font-medium text-gray-700 mb-2">活跃成员</p>
            <div class="flex -space-x-2">
              <img
                v-for="member in guild.activeMembers.slice(0, 5)"
                :key="member.id"
                :src="member.avatar"
                :alt="member.name"
                :title="member.name"
                class="w-8 h-8 rounded-full border-2 border-white object-cover"
              />
              <div
                v-if="guild.activeMembers.length > 5"
                class="w-8 h-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-600"
              >
                +{{ guild.activeMembers.length - 5 }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建/编辑工会对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingGuild ? '编辑工会' : '创建工会'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="guildForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="工会名称" prop="name">
          <el-input v-model="guildForm.name" placeholder="请输入工会名称" />
        </el-form-item>
        <el-form-item label="工会描述" prop="description">
          <el-input
            v-model="guildForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入工会描述"
          />
        </el-form-item>
        <el-form-item label="会长" prop="leader">
          <el-input v-model="guildForm.leader" placeholder="请输入会长姓名" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="guildForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="guildForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="分成比例" prop="commissionRate">
          <el-input-number
            v-model="guildForm.commissionRate"
            :min="0"
            :max="100"
            :precision="1"
            style="width: 100%"
          />
          <span class="text-sm text-gray-500 ml-2">%</span>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="guildForm.status">
            <el-radio label="active">活跃</el-radio>
            <el-radio label="inactive">暂停</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="saveGuild" :loading="saving">
            {{ editingGuild ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 工会详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="工会详情" width="800px">
      <div v-if="selectedGuild" class="space-y-6">
        <!-- 工会基本信息 -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
          <h3 class="text-2xl font-bold mb-2">{{ selectedGuild.name }}</h3>
          <p class="text-blue-100">{{ selectedGuild.description }}</p>
        </div>

        <!-- 统计信息 -->
        <div class="grid grid-cols-4 gap-4">
          <div class="bg-blue-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-blue-600">{{ selectedGuild.memberCount }}</p>
            <p class="text-sm text-gray-600">成员数量</p>
          </div>
          <div class="bg-green-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-green-600">¥{{ formatNumber(selectedGuild.totalIncome) }}</p>
            <p class="text-sm text-gray-600">总收益</p>
          </div>
          <div class="bg-yellow-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-yellow-600">{{ selectedGuild.commissionRate }}%</p>
            <p class="text-sm text-gray-600">分成比例</p>
          </div>
          <div class="bg-purple-50 rounded-lg p-4 text-center">
            <p class="text-2xl font-bold text-purple-600">{{ selectedGuild.activeMembers.length }}</p>
            <p class="text-sm text-gray-600">活跃成员</p>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="grid grid-cols-2 gap-6">
          <div>
            <h4 class="font-semibold text-gray-900 mb-3">基本信息</h4>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-500">会长</span>
                <span class="font-medium">{{ selectedGuild.leader }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">联系电话</span>
                <span>{{ selectedGuild.phone }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">邮箱</span>
                <span>{{ selectedGuild.email }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-500">创建时间</span>
                <span>{{ formatDate(selectedGuild.createdAt) }}</span>
              </div>
            </div>
          </div>
          <div>
            <h4 class="font-semibold text-gray-900 mb-3">成员列表</h4>
            <div class="space-y-2 max-h-40 overflow-y-auto">
              <div
                v-for="member in selectedGuild.activeMembers"
                :key="member.id"
                class="flex items-center space-x-3 p-2 rounded hover:bg-gray-50"
              >
                <img
                  :src="member.avatar"
                  :alt="member.name"
                  class="w-8 h-8 rounded-full object-cover"
                />
                <div class="flex-1">
                  <p class="text-sm font-medium">{{ member.name }}</p>
                  <p class="text-xs text-gray-500">等级 {{ member.level }}</p>
                </div>
                <span class="text-sm text-green-600">¥{{ member.income }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 成员管理对话框 -->
    <el-dialog v-model="showMemberDialog" title="成员管理" width="900px">
      <div v-if="selectedGuild">
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-semibold text-gray-900">{{ selectedGuild.name }} - 成员管理</h4>
          <el-button type="primary" size="small" @click="showAddMemberDialog = true">
            <UserPlus class="w-4 h-4 mr-1" />
            添加成员
          </el-button>
        </div>
        
        <el-table :data="selectedGuild.activeMembers" style="width: 100%">
          <el-table-column label="成员信息" min-width="200">
            <template #default="{ row }">
              <div class="flex items-center space-x-3">
                <img
                  :src="row.avatar"
                  :alt="row.name"
                  class="w-10 h-10 rounded-full object-cover"
                />
                <div>
                  <p class="font-medium">{{ row.name }}</p>
                  <p class="text-sm text-gray-500">ID: {{ row.id }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="level" label="等级" width="80" />
          <el-table-column label="收益" width="120">
            <template #default="{ row }">
              <span class="font-medium text-green-600">¥{{ row.income }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template #default="{ row }">
              <el-button size="small" @click="viewMember(row)">
                <Eye class="w-4 h-4" />
              </el-button>
              <el-button size="small" type="danger" @click="removeMember(row)">
                <UserMinus class="w-4 h-4" />
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Building,
  Users,
  DollarSign,
  TrendingUp,
  MoreVertical,
  Eye,
  Edit,
  Trash2,
  UserPlus,
  UserMinus
} from 'lucide-vue-next'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const showMemberDialog = ref(false)
const showAddMemberDialog = ref(false)
const editingGuild = ref(null)
const selectedGuild = ref(null)
const formRef = ref()

// 工会表单
const guildForm = ref({
  name: '',
  description: '',
  leader: '',
  phone: '',
  email: '',
  commissionRate: 50,
  status: 'active'
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入工会名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入工会描述', trigger: 'blur' }],
  leader: [{ required: true, message: '请输入会长姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 工会统计数据
const guildStats = ref([
  {
    title: '工会总数',
    value: '3',
    icon: Building,
    bgColor: 'bg-blue-50',
    iconColor: 'text-blue-600'
  },
  {
    title: '总成员数',
    value: '156',
    icon: Users,
    bgColor: 'bg-green-50',
    iconColor: 'text-green-600'
  },
  {
    title: '总收益',
    value: '¥2,580,900',
    icon: DollarSign,
    bgColor: 'bg-yellow-50',
    iconColor: 'text-yellow-600'
  },
  {
    title: '平均增长',
    value: '+15%',
    icon: TrendingUp,
    bgColor: 'bg-purple-50',
    iconColor: 'text-purple-600'
  }
])

// 模拟工会数据
const guilds = ref([
  {
    id: 'G001',
    name: '星光工会',
    description: '专注于娱乐直播的顶级工会',
    leader: '张总',
    phone: '13800138000',
    email: '<EMAIL>',
    memberCount: 68,
    totalIncome: 1280500,
    commissionRate: 60,
    status: 'active',
    createdAt: '2023-06-15',
    activeMembers: [
      {
        id: 'S001',
        name: '小美',
        avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=beautiful%20female%20streamer%20avatar%20cartoon%20style&image_size=square',
        level: 15,
        income: 25800
      },
      {
        id: 'S002',
        name: '阳光少女',
        avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=cute%20girl%20streamer%20avatar%20anime%20style&image_size=square',
        level: 12,
        income: 18900
      },
      {
        id: 'S003',
        name: '游戏达人',
        avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=gaming%20streamer%20avatar%20modern%20style&image_size=square',
        level: 20,
        income: 34200
      }
    ]
  },
  {
    id: 'G002',
    name: '梦想工会',
    description: '年轻有活力的新兴工会',
    leader: '李经理',
    phone: '13900139000',
    email: '<EMAIL>',
    memberCount: 45,
    totalIncome: 680200,
    commissionRate: 55,
    status: 'active',
    createdAt: '2023-08-20',
    activeMembers: [
      {
        id: 'S004',
        name: '甜心主播',
        avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=sweet%20female%20streamer%20avatar%20kawaii%20style&image_size=square',
        level: 10,
        income: 15600
      },
      {
        id: 'S005',
        name: '音乐天使',
        avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=music%20streamer%20avatar%20artistic%20style&image_size=square',
        level: 14,
        income: 22400
      }
    ]
  },
  {
    id: 'G003',
    name: '彩虹工会',
    description: '多元化内容的综合性工会',
    leader: '王会长',
    phone: '13700137000',
    email: '<EMAIL>',
    memberCount: 43,
    totalIncome: 620200,
    commissionRate: 58,
    status: 'active',
    createdAt: '2023-09-10',
    activeMembers: [
      {
        id: 'S006',
        name: '舞蹈精灵',
        avatar: 'https://trae-api-sg.mchost.guru/api/ide/v1/text_to_image?prompt=dance%20streamer%20avatar%20elegant%20style&image_size=square',
        level: 16,
        income: 28900
      }
    ]
  }
])

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

// 格式化日期
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

// 查看工会详情
const viewGuild = (guild: any) => {
  selectedGuild.value = guild
  showDetailDialog.value = true
}

// 编辑工会
const editGuild = (guild: any) => {
  editingGuild.value = guild
  guildForm.value = { ...guild }
  showAddDialog.value = true
}

// 成员管理
const manageMembers = (guild: any) => {
  selectedGuild.value = guild
  showMemberDialog.value = true
}

// 删除工会
const deleteGuild = async (guild: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除工会 ${guild.name} 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟API调用
    const index = guilds.value.findIndex(g => g.id === guild.id)
    if (index !== -1) {
      guilds.value.splice(index, 1)
    }
    ElMessage.success('工会删除成功')
  } catch {
    // 用户取消操作
  }
}

// 保存工会
const saveGuild = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (editingGuild.value) {
      // 更新现有工会
      const index = guilds.value.findIndex(g => g.id === editingGuild.value.id)
      if (index !== -1) {
        guilds.value[index] = { ...guilds.value[index], ...guildForm.value }
      }
      ElMessage.success('工会信息更新成功')
    } else {
      // 创建新工会
      const newGuild = {
        ...guildForm.value,
        id: `G${String(guilds.value.length + 1).padStart(3, '0')}`,
        memberCount: 0,
        totalIncome: 0,
        createdAt: new Date().toISOString().split('T')[0],
        activeMembers: []
      }
      guilds.value.push(newGuild)
      ElMessage.success('工会创建成功')
    }
    
    showAddDialog.value = false
    resetForm()
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saving.value = false
  }
}

// 查看成员
const viewMember = (member: any) => {
  ElMessage.info(`查看成员 ${member.name} 的详细信息`)
}

// 移除成员
const removeMember = async (member: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要将 ${member.name} 从工会中移除吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 模拟API调用
    const memberIndex = selectedGuild.value.activeMembers.findIndex(m => m.id === member.id)
    if (memberIndex !== -1) {
      selectedGuild.value.activeMembers.splice(memberIndex, 1)
      selectedGuild.value.memberCount--
    }
    ElMessage.success('成员移除成功')
  } catch {
    // 用户取消操作
  }
}

// 重置表单
const resetForm = () => {
  editingGuild.value = null
  guildForm.value = {
    name: '',
    description: '',
    leader: '',
    phone: '',
    email: '',
    commissionRate: 50,
    status: 'active'
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>